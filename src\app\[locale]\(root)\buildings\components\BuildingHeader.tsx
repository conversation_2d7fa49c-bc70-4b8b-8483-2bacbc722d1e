import React from "react";
import { useTranslations } from "next-intl";
import { Badge } from "@/components/ui/badge";
import { Building2, MapPin } from "lucide-react";

export default function BuildingHeader() {
  const t = useTranslations("buildings");

  return (
    <div className="mb-12 text-center">
      <div className="mb-6">
        <Badge className="bg-primary/10 text-primary border-primary/20 mb-4">
          <Building2 className="mr-1 h-4 w-4" />
          {t("exploreBuilding")}
        </Badge>
        <h1 className="text-primary mb-4 text-4xl leading-tight font-bold md:text-5xl lg:text-6xl">
          {t("title")}
        </h1>
        <p className="mx-auto max-w-3xl text-lg text-gray-600 md:text-xl">
          {t("description")}
        </p>
      </div>

      {/* Stats or additional info could go here */}
      <div className="flex items-center justify-center gap-6 text-sm text-gray-500">
        <div className="flex items-center gap-1">
          <MapPin className="h-4 w-4" />
          <span>{t("multipleLocations") || "Multiple Locations"}</span>
        </div>
        <div className="flex items-center gap-1">
          <Building2 className="h-4 w-4" />
          <span>{t("premiumBuildings") || "Premium Buildings"}</span>
        </div>
      </div>
    </div>
  );
}
