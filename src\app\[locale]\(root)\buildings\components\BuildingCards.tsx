import { Building } from "@/types/buildings";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import { ArrowRight, MapPin, Building2 } from "lucide-react";
import { useLocale, useTranslations } from "next-intl";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";

interface BuildingCardsProps {
  data: Building[];
  isLoading?: boolean;
}

export default function BuildingCards({
  data,
  isLoading = false,
}: BuildingCardsProps) {
  const locale = useLocale();
  const t = useTranslations("buildings");

  if (isLoading) {
    return <BuildingCardsLoading />;
  }

  if (!data || data.length === 0) {
    return <BuildingCardsEmpty />;
  }

  return (
    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
      {data.map((build, index) => (
        <Card
          key={build.id}
          className="group shadow-soft hover:shadow-elevated overflow-hidden border-0 bg-white transition-all duration-300 hover:-translate-y-1"
          style={{ animationDelay: `${index * 100}ms` }}
        >
          <div className="relative overflow-hidden">
            <div className="relative aspect-[4/3]">
              <Image
                alt={locale === "ar" ? build.nameAr : build.nameEn}
                className="h-full w-full object-cover transition-all duration-500 group-hover:scale-110"
                src={build.image}
                fill
                sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, (max-width: 1280px) 33vw, 25vw"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />

              {/* City Badge */}
              <Badge className="absolute top-3 left-3 border-0 bg-white/90 text-gray-800 backdrop-blur-sm">
                <Building2 className="mr-1 h-3 w-3" />
                {locale === "ar" ? build.cityNameAr : build.cityNameEn}
              </Badge>

              {/* Units Count Badge */}
              <Badge className="bg-primary/90 absolute top-3 right-3 border-0 text-white backdrop-blur-sm">
                {build.unitsCount}+ {t("unitsCount")}
              </Badge>
            </div>

            {/* Content Overlay */}
            <div className="absolute inset-x-0 bottom-0 translate-y-2 transform p-4 text-white opacity-0 transition-all duration-300 group-hover:translate-y-0 group-hover:opacity-100">
              <Link
                href={`/units?buildingId=${build.id}`}
                className="inline-flex items-center gap-2 rounded-full border border-white/30 bg-white/20 px-3 py-2 text-sm font-medium text-white shadow-lg backdrop-blur-sm transition-all duration-300 hover:border-white/50 hover:bg-white/30"
              >
                <MapPin className="h-4 w-4" />
                <span>{t("viewUnits")}</span>
                <ArrowRight
                  className={`h-4 w-4 transition-transform duration-300 group-hover:translate-x-1 ${locale === "ar" ? "rotate-180" : ""}`}
                />
              </Link>
            </div>
          </div>

          {/* Card Content */}
          <div className="p-4">
            <h3 className="mb-2 line-clamp-1 text-lg font-semibold text-gray-900">
              {locale === "ar" ? build.nameAr : build.nameEn}
            </h3>
            <p className="mb-3 line-clamp-2 text-sm text-gray-600">
              {locale === "ar" ? build.descriptionAr : build.descriptionEn}
            </p>
            <div className="flex items-center justify-between">
              <span className="text-sm text-gray-500">
                {locale === "ar" ? build.cityNameAr : build.cityNameEn}
              </span>
              <Link
                href={`/units?buildingId=${build.id}`}
                className="text-primary hover:text-primary/80 text-sm font-medium transition-colors"
              >
                {t("viewUnits")}
              </Link>
            </div>
          </div>
        </Card>
      ))}
    </div>
  );
}

// Loading State Component
function BuildingCardsLoading() {
  return (
    <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
      {Array.from({ length: 8 }).map((_, index) => (
        <Card
          key={index}
          className="shadow-soft overflow-hidden border-0 bg-white"
        >
          <div className="aspect-[4/3] animate-pulse bg-gray-200" />
          <div className="space-y-3 p-4">
            <div className="h-5 animate-pulse rounded bg-gray-200" />
            <div className="h-4 w-3/4 animate-pulse rounded bg-gray-200" />
            <div className="h-4 w-1/2 animate-pulse rounded bg-gray-200" />
          </div>
        </Card>
      ))}
    </div>
  );
}

// Empty State Component
function BuildingCardsEmpty() {
  const t = useTranslations("buildings");

  return (
    <div className="flex flex-col items-center justify-center py-16 text-center">
      <div className="mb-6 rounded-full bg-gray-100 p-6">
        <Building2 className="h-12 w-12 text-gray-400" />
      </div>
      <h3 className="mb-2 text-xl font-semibold text-gray-900">
        {t("noBuildings")}
      </h3>
      <p className="max-w-md text-gray-600">{t("noBuildingsDescription")}</p>
    </div>
  );
}
