{"metadata": {"title": "<PERSON><PERSON> - The future of living"}, "global": {"cancel": "Cancel", "close": "Close", "continue": "Continue", "confirm": "Confirm", "showMore": "Show More"}, "formFeedback": {"title": "Thank you for your message!", "description": "We will get back to you as soon as possible."}, "validations": {"required": "This field is required", "email": "Please enter a valid email address", "minLength": "Must be at least {min} characters", "maxLength": "Must be at most {max} characters", "phoneDigitsRange": "Phone number must be between {min} and {max} digits", "url": "Please enter a valid URL", "password": {"min": "Password must be at least {min} characters", "match": "Passwords don't match", "uppercase": "Password must contain at least one uppercase letter", "number": "Password must contain at least one number", "special": "Password must contain at least one special character"}, "phoneNumber": "Please enter a valid phone number"}, "register": {"conflictError": "This email or phone number is already in use", "success": "Registration successful! Please login"}, "login": {"error": "Invalid email or password"}, "errors": {"serverError": "Server Error", "generalError": "Something went wrong"}, "nav": {"home": "Home", "about": "About", "ourStory": "Our Story", "howItWorks": "How it Works", "careers": "Careers", "press": "Press", "corporateStay": "Corporate Stay", "contact": "Contact us", "realEstate": "Real Estate to Investors", "login": "<PERSON><PERSON>", "signup": "Sign up", "menu": "<PERSON><PERSON>", "navigate": "Navigate through Hala"}, "account": {"title": "Profile", "profile": {"title": "Profile", "activation": "Activation", "preferences": "Preferences", "basicInformation": "Basic Information", "firstName": "First Name", "lastName": "Last Name", "emailAddress": "Email Address", "phoneNumber": "Phone Number", "phoneNotProvided": "Phone number not provided", "edit": "Edit", "save": "Save", "saving": "Saving...", "cancel": "Cancel", "reset": "Reset", "loadingProfile": "Loading profile...", "failedToLoadProfile": "Failed to load profile", "tryAgain": "Try Again", "anErrorOccurred": "An error occurred", "profilePicture": "Profile Picture", "uploadProfilePicture": "Upload profile picture", "chooseImage": "Choose an image"}, "changePassword": {"title": "Change Password", "description": "Update your password to keep your account secure", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm New Password", "changingPassword": "Changing Password...", "updatePassword": "Update Password", "currentPasswordRequired": "Current password is required", "passwordMinLength": "Password must be at least 8 characters", "passwordUppercase": "Password must contain at least one uppercase letter", "passwordLowercase": "Password must contain at least one lowercase letter", "passwordNumber": "Password must contain at least one number", "passwordSpecial": "Password must contain at least one special character", "confirmPasswordRequired": "Please confirm your password", "passwordsDontMatch": "Passwords don't match"}, "adminResponse": {"title": "Admin Messages", "message": "Please add your national ID to complete your profile verification.", "verification": {"needed": "Verification Needed", "uploadDocuments": "Please upload your ID documents for verification"}, "review": {"underReview": "Under Review", "documentsBeingReviewed": "Your documents are being reviewed by our team"}, "approved": {"verified": "Verified", "accountVerified": "Your account has been successfully verified"}, "refused": {"verificationFailed": "Verification Failed", "defaultMessage": "Your verification was not approved. Please contact support for assistance."}}, "preferences": {"title": "Preferences"}, "activation": {"title": "Activation Info", "edit": "Edit", "cancel": "Cancel", "loadingProfile": "Loading profile information...", "errorLoadingProfile": "Error loading profile information", "birthdateGregorian": "Birthdate (Gregorian)", "birthdateHijri": "Birthdate (Hijri)", "nationality": "Nationality", "documentUpload": "Document Upload", "dragDropFiles": "Drag and drop files here, or click to select", "chooseFiles": "<PERSON><PERSON>", "uploadedFiles": "Uploaded Files", "completed": "Completed", "uploading": "Uploading...", "noFilesUploaded": "No files uploaded", "uploadDocumentsMessage": "Upload documents to complete your profile", "noDocumentsMessage": "No documents have been uploaded yet", "saving": "Saving...", "saveChanges": "Save Changes"}}, "hero": {"title": "The future", "subtitle": "of living", "description": "Discover and stay in your favourite neighbourhoods around the world", "badge": "✨ Experience Luxury Living", "trustIndicators": {"rating": "4.9 Rating", "happyGuests": "50K+ Happy Guests", "awardWinning": "Award Winning"}}, "search": {"city": "CITY", "selectCity": "Select city", "checkIn": "CHECK-IN", "checkOut": "CHECK-OUT", "guests": "GUESTS", "selectDate": "Select date", "guest": "guest", "guestsPlural": "guests", "search": "Search"}, "cities": {"title": "Our Locations", "description": "Discover our premium accommodations across major cities", "viewUnits": "View Buildings", "buildings": "Buildings", "riyadh": "Riyadh", "jeddah": "Jeddah", "mecca": "Mecca", "medina": "Medina", "dammam": "<PERSON><PERSON><PERSON>", "khobar": "Khobar", "taif": "<PERSON><PERSON>", "abha": "<PERSON><PERSON>", "tabuk": "Tabuk", "hail": "<PERSON>l", "dubai": "Dubai", "abuDhabi": "Abu Dhabi", "sharjah": "Sharjah", "doha": "Doha", "kuwait": "Kuwait", "manama": "Manama", "muscat": "Muscat", "cairo": "Cairo", "casablanca": "Casablanca", "istanbul": "Istanbul", "makkah": "<PERSON><PERSON><PERSON>", "madinah": "<PERSON><PERSON><PERSON>"}, "buildings": {"title": "Buildings in our Areas", "description": "Discover our premium accommodations across major cities", "viewUnits": "View Units", "unitsCount": "Units", "exploreBuilding": "Explore Building", "location": "Location", "availableUnits": "Available Units", "noBuildings": "No buildings found", "noBuildingsDescription": "Try adjusting your search criteria or check back later", "loadingBuildings": "Loading buildings...", "buildingDetails": "Building Details", "amenities": "Amenities", "bookNow": "Book Now", "checkAvailability": "Check Availability", "pricePerNight": "per night", "totalUnits": "Total Units", "buildingType": "Building Type", "yearBuilt": "Year Built", "floors": "Floors", "parking": "Parking Available", "wifi": "Free WiFi", "gym": "Fitness Center", "pool": "Swimming Pool", "security": "24/7 Security", "concierge": "Concierge Service"}, "destinations": {"title": "Where next?", "description": "Explore our curated selection of the world's most vibrant neighborhoods", "dubai": {"name": "Dubai", "description": "Modern luxury in the heart of the desert"}, "london": {"name": "London", "description": "Historic charm meets contemporary living"}, "newYork": {"name": "New York", "description": "The city that never sleeps"}, "explore": "Explore"}, "features": {"title": "Why choose <PERSON><PERSON>?", "description": "We're reimagining how people live and travel, providing premium accommodations with the service and amenities you deserve", "badge": "Why Choose Hala", "excellence": "Experience excellence in every detail", "quickBooking": {"title": "Quick & Seamless Booking", "description": "Book your complete unit electronically from anywhere"}, "securePayment": {"title": "Secure & Trusted Payment", "description": "Integration with certified payment gateways and protection of your data"}, "whatsappServices": {"title": "WhatsApp Services", "description": "Request cleaning, maintenance, delivery or meals directly through WhatsApp easily"}, "customerSupport": {"title": "24/7 Customer Support", "description": "Expert assistance available around the clock for all your needs"}, "flexibleCancellation": {"title": "Flexible Cancellation", "description": "Free cancellation up to 24 hours before check-in with full refund"}, "premiumAmenities": {"title": "Premium Amenities", "description": "Luxury furnishings, high-speed WiFi, and premium linens included"}, "integratedManagement": {"title": "Integrated Management", "description": "Smart dashboard with real-time updates and automated processes"}, "primeLocations": {"title": "Prime Locations", "description": "Stay in the heart of the world's most vibrant neighborhoods"}, "categories": {"all": "All Features", "booking": "Booking", "support": "Support", "amenities": "Amenities", "payment": "Payment", "policy": "Policies", "management": "Management", "location": "Location"}, "benefits": {"instant-confirmation": "Instant Confirmation", "mobile-friendly": "Mobile Friendly", "real-time-availability": "Real-time Availability", "ssl-encryption": "SSL Encryption", "multiple-currencies": "Multiple Currencies", "fraud-protection": "<PERSON><PERSON>", "24-7-availability": "24/7 Availability", "instant-response": "Instant Response", "multilingual": "Multilingual", "24-7-support": "24/7 Support", "expert-team": "Expert Team", "quick-resolution": "Quick Resolution", "free-cancellation": "Free Cancellation", "full-refund": "Full Refund", "easy-modification": "Easy Modification", "luxury-furnishing": "<PERSON><PERSON><PERSON> Furnishing", "high-speed-wifi": "High-speed WiFi", "premium-linens": "Premium Linens", "smart-dashboard": "Smart Dashboard", "real-time-updates": "Real-time Updates", "automated-processes": "Automated Processes", "city-center": "City Center", "transport-access": "Transport Access", "local-attractions": "Local Attractions"}}, "featured": {"unitsSection": {"badge": "Available Units", "title": "Featured Units for Rent", "description": "Choose from a diverse collection of fully furnished residential units", "viewDetails": "View Details", "bedrooms": "{{count}} Bedroom", "bedrooms_plural": "{{count}} Bedrooms", "bathrooms": "{{count}} Bathroom", "bathrooms_plural": "{{count}} Bathrooms", "sqm": "sqm"}, "offersSection": {"badge": "Special Offers", "title": "Exclusive Deals & Discounts", "description": "Seize the moment—book now at the best prices", "discount": "Discount", "limitedOffer": "Limited Offer", "validUntil": "Valid until", "bookNow": "Book Now"}, "units": {"1": {"title": "Luxury Apartment in Center", "price": "SAR 2,500/month"}, "2": {"title": "Modern Studio", "price": "SAR 1,800/month"}, "3": {"title": "Penthouse with View", "price": "SAR 4,200/month"}, "4": {"title": "Contemporary Villa", "price": "SAR 5,800/month"}, "5": {"title": "Spacious Family Apartment", "price": "SAR 3,200/month"}, "6": {"title": "Loft with Terrace", "price": "SAR 2,900/month"}}, "offers": {"1": {"title": "Long Stay Offer", "description": "20% off stays longer than 30 days", "validUntil": "31 December 2024"}, "2": {"title": "All-inclusive Services Package", "description": "Cleaning + Maintenance + Meals at a reduced price", "originalPrice": "SAR 500"}, "3": {"title": "New Customers Offer", "description": "15% off your first stay", "validUntil": "End of the month"}}}, "common": {"learnMore": "Learn More"}, "stats": {"title": "Trusted by travelers worldwide", "description": "Join thousands of satisfied guests who have made Hal<PERSON> their home away from home", "happyGuests": "Happy Guests", "premiumProperties": "Premium Properties", "satisfactionRate": "Satisfaction Rate", "hourSupport": "Hour Support"}, "testimonials": {"subHeading": "Guest Stories", "title": "What our guests say", "description": "Don't just take our word for it - hear from travelers who've experienced the Hala difference.", "sarah": {"text": "<PERSON><PERSON> made my business trip to Dubai absolutely seamless. The apartment was modern, well-located, and had everything I needed. Will definitely book again!"}, "marcus": {"text": "The attention to detail was incredible. From the welcome package to the local recommendations, <PERSON><PERSON> went above and beyond my expectations."}, "elena": {"text": "Finally found a service that understands what modern travelers need. Clean, comfortable, and in the perfect neighborhood. Highly recommended!"}, "addReview": {"button": "Share Your Experience", "title": "Share Your Experience", "description": "Help other travelers by sharing your luxury accommodation experience", "rating": "Your Rating", "review": "Your Review", "placeholder": "Tell us about your experience...", "submit": "Submit Review", "submitting": "Submitting...", "success": {"title": "Review Submitted!", "description": "Thank you for sharing your experience. Your review has been submitted successfully."}, "error": {"title": "Review Required", "ratingRequired": "Please select a rating before submitting your review.", "reviewRequired": "Please write your review before submitting.", "reviewTooShort": "Please write a review that is at least 10 characters long.", "submissionFailed": "There was an error submitting your review. Please try again."}}}, "footer": {"description": "Reimagining the future of living with premium accommodations in the world's most vibrant neighborhoods.", "company": "Company", "aboutUs": "About Us", "careers": "Careers", "press": "Press", "blog": "Blog", "investorRelations": "Investor Relations", "support": "Support", "helpCenter": "Help Center", "contactUs": "Contact Us", "safetySecurity": "Safety & Security", "communityGuidelines": "Community Guidelines", "accessibility": "Accessibility", "stayUpdated": "Stay Updated", "newsletterDescription": "Get the latest news and exclusive offers delivered to your inbox.", "emailPlaceholder": "Enter your email", "subscribe": "Subscribe", "copyright": "© 2024 Hala. All rights reserved.", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service", "cookiePolicy": "<PERSON><PERSON>"}, "newsletter": {"stayConnected": "Stay Connected", "title": "Stay Updated", "subtitle": "Insights & Deals", "description": "Join thousands of savvy travelers and get access to insider tips, exclusive discounts, and early access to new destinations.", "benefits": {"exclusiveDeals": {"title": "Exclusive Deals", "description": "Get early access to special offers and discounts"}, "premiumContent": {"title": "Premium Content", "description": "Receive curated travel guides and insider tips"}, "firstToKnow": {"title": "First to Know", "description": "Be notified about new destinations and features"}}, "subscriberCount": "10K+ subscribers", "rating": "4.9/5 rating", "noSpam": "No spam, ever", "formTitle": "Join Our Newsletter", "formDescription": "Get weekly travel inspiration delivered to your inbox", "emailPlaceholder": "Enter your email address", "subscribeButton": "Subscribe Now", "privacyText": "By subscribing, you agree to our Privacy Policy and Terms of Service.", "unsubscribeText": "Unsubscribe anytime with one click.", "successTitle": "Welcome to the Hala Community! 🎉", "successDescription": "Thank you for subscribing! Check your inbox for a special welcome offer.", "joinSubscribers": "Join 10K+ subscribers", "successRating": "Rated 4.9/5"}, "cta": {"badge": "Ready to Start Your Journey?", "title": "Experience Luxury Living", "subtitle": "Around the World", "description": "Join thousands of satisfied guests who have discovered the perfect blend of comfort, luxury, and authentic local experiences with <PERSON><PERSON>.", "stats": {"happyGuests": "Happy Guests", "premiumProperties": "Premium Properties", "averageRating": "Average Rating"}, "buttons": {"exploreProperties": "Explore Units", "learnMore": "Learn More"}, "trustIndicators": {"trustedWorldwide": "Trusted by travelers worldwide", "customerSupport": "24/7 customer support", "globalDestinations": "Global destinations"}}, "hotelGallery": {"title": "Explore Our Hotel", "description": "Discover the finest amenities and spaces designed for your comfort", "exploreMore": "Explore more", "facilities": {"grandLobby": "Grand Lobby", "fineDining": "Fine Dining Restaurant", "luxurySpa": "Luxury Spa & Wellness", "infinityPool": "Infinity Pool", "fitnessCenter": "Fitness Center", "premiumSuites": "Premium Suites", "conferenceHalls": "Conference Halls", "gardenTerrace": "Garden Terrace"}}, "luxuryExperience": {"title": "Redefining Luxury", "subtitle": "Experience Unparalleled Comfort", "description": "Immerse yourself in a world of luxury where every detail is crafted to perfection. Our premium accommodations offer modern amenities, breathtaking views, and personalized service that exceeds expectations.", "features": {"concierge": "24/7 Concierge Service", "amenities": "Premium Room Amenities", "dining": "World-Class Restaurants", "spa": "Spa & Wellness Center"}, "watchVideo": "Watch Experience Video", "imageAlt": "Luxury Hotel Room"}, "faq": {"title": "Frequently Asked Questions", "subtitle": "Got Questions?", "description": "Find answers to common questions about booking, stays, and our services.", "questions": {"howToBook": {"question": "How do I book a property on Hala?", "answer": "Booking is simple! Use our search form to find your perfect accommodation, select your dates and number of guests, then click 'Book Now'. You'll be guided through our secure payment process."}, "whatIncluded": {"question": "What is included in the booking?", "answer": "All our properties come with high-speed WiFi, 24/7 concierge support, professional cleaning, and premium amenities. Specific inclusions vary by property and are clearly listed in each property description."}, "cancellation": {"question": "Can I cancel or modify my booking?", "answer": "Yes! We offer flexible cancellation policies. Most bookings can be modified or cancelled up to 24-48 hours before check-in. Check your booking confirmation for specific terms."}, "access": {"question": "How do I access the property?", "answer": "You'll receive detailed check-in instructions 24 hours before arrival, including keyless entry codes or key collection details. Our concierge team is available 24/7 to assist with any access issues."}, "help": {"question": "What if I need help during my stay?", "answer": "Our 24/7 concierge team is always available via phone, chat, or our mobile app. We can help with everything from restaurant recommendations to emergency assistance."}, "pets": {"question": "Are pets allowed?", "answer": "Pet policies vary by property. Look for the pet-friendly icon when searching, or contact our team to find accommodations that welcome your furry friends."}}, "stillHaveQuestions": "Still have questions?", "supportDescription": "Our support team is here to help you 24/7", "liveChat": "Live Chat", "contactSupport": "Contact Support"}, "units": {"title": "Explore Our Units", "subtitle": "Discover Lu<PERSON>ury Accommodations", "description": "Discover a diverse collection of luxury units in the world's finest locations", "search": {"placeholder": "Search units...", "unitType": "Unit Type", "allTypes": "All Types", "apartment": "Apartment", "studio": "Studio", "villa": "Villa", "suite": "Suite", "penthouse": "Penthouse", "loft": "Loft"}, "sort": {"sortBy": "Sort by", "featured": "Featured", "priceLowToHigh": "Price: Low to High", "priceHighToLow": "Price: High to Low", "rating": "Rating"}, "filters": {"filters": "Filters", "priceRange": "Price Range", "amenities": "Amenities", "clearFilters": "Clear Filters"}, "viewMode": {"grid": "Grid View", "list": "List View"}, "results": {"found": "Found {count} units", "total": "Total units: {total}", "noResults": "No results found", "noResultsDescription": "Try adjusting your filters or search for something else", "clearFilters": "Clear Filters"}, "property": {"perNight": "per night", "bedrooms": "bedrooms", "bathrooms": "bathrooms", "guests": "guests", "moreAmenities": "+{count} more"}}, "about": {"title": "About Hala", "subtitle": "Our Story", "description": "We're reimagining the future of luxury living by creating exceptional accommodation experiences in the world's most vibrant neighborhoods. Our mission is to provide travelers with premium, technology-enabled stays that feel like home.", "stats": {"happyGuests": "Happy Guests", "cities": "Cities Worldwide", "rating": "Average Rating", "support": "Support Available"}, "mission": {"subtitle": "Our Mission", "title": "Redefining Luxury Travel", "description": "At Hala, we believe that exceptional travel experiences should be accessible, seamless, and memorable. We combine cutting-edge technology with personalized service to create accommodations that exceed expectations.", "points": {"luxury": "Premium accommodations in prime locations worldwide", "technology": "Seamless booking and check-in experience through our platform", "service": "24/7 concierge support and personalized guest services"}, "award": {"title": "Award-Winning Excellence", "description": "Recognized as the leading luxury accommodation platform with industry awards for innovation, service quality, and guest satisfaction."}}, "values": {"subtitle": "Our Values", "title": "What Drives Us", "description": "Our core values guide everything we do, from selecting properties to serving our guests.", "excellence": {"title": "Excellence", "description": "We maintain the highest standards in every aspect of our service, from property selection to guest experience."}, "trust": {"title": "Trust & Security", "description": "Your safety and privacy are paramount. We implement robust security measures and transparent policies."}, "innovation": {"title": "Innovation", "description": "We continuously evolve our platform and services using the latest technology to enhance your experience."}, "community": {"title": "Community", "description": "We build lasting relationships with guests, hosts, and local communities in every destination we serve."}}, "timeline": {"subtitle": "Our Journey", "title": "Milestones & Growth", "description": "From a vision to a global platform, here's how we've grown to serve luxury travelers worldwide."}, "milestones": {"founded": {"title": "Hala Founded", "description": "Started with a vision to revolutionize luxury accommodation experiences through technology and exceptional service."}, "expansion": {"title": "Global Expansion", "description": "Expanded to 25+ cities worldwide, partnering with premium properties and establishing our concierge network."}, "recognition": {"title": "Industry Recognition", "description": "Received multiple awards for innovation in travel technology and excellence in customer service."}, "global": {"title": "Global Leadership", "description": "Now serving 100+ cities with 50,000+ satisfied guests and continuing to set new standards in luxury travel."}}, "cta": {"title": "Ready to Experience Hala?", "description": "Join thousands of travelers who have discovered the perfect blend of luxury, technology, and personalized service.", "explore": "Explore Units", "contact": "Contact Us"}}, "contact": {"badge": "Get in Touch", "title": "Contact Us", "description": "Have questions or need assistance? Our dedicated support team is here to help you 24/7. Reach out to us through any of the channels below.", "trustIndicators": {"rating": "Customer Rating", "customers": "Happy Customers", "support": "Support Available"}, "methods": {"title": "Get in Touch", "description": "Choose the most convenient way to reach our support team", "email": {"title": "Email Support", "description": "Send us a detailed message and we'll get back to you within 24 hours"}, "phone": {"title": "Phone Support", "description": "Speak directly with our support team for immediate assistance"}, "whatsapp": {"title": "WhatsApp Support", "description": "Chat with us on WhatsApp for quick responses and support"}}, "form": {"title": "Send us a Message", "description": "Fill out the form below and we'll get back to you as soon as possible", "name": "Full Name", "namePlaceholder": "Enter your full name", "email": "Email Address", "emailPlaceholder": "Enter your email address", "phone": "Phone Number", "phonePlaceholder": "Enter your phone number", "companyName": "Company Name", "location": "Property Location", "companyNamePlaceholder": "Enter your company name", "companyWebsite": "Company Website/LinkedIn", "companyWebsitePlaceholder": "Enter your company website or LinkedIn profile", "message": "Message", "messagePlaceholder": "Tell us more about your inquiry...", "submit": "Send Message", "sending": "Sending...", "successMessage": "Message sent successfully!", "successDescription": "Thank you for contacting us. We'll get back to you within 24 hours.", "reason": "Reason of contact", "reasonPlaceholder": "Select reason for contact", "reasons": {"generalBooking": "General Booking Inquiry", "delaysConcerns": "Delays & Concerns", "groupBooking": "Group Booking", "mediaPress": "Media & Press", "employment": "Employment Opportunities", "businessPartnership": "Business Partnership", "other": "Other"}}, "support": {"title": "Why Choose Our Support?", "description": "We're committed to providing exceptional customer service with a personal touch. Our team is trained to handle all your needs efficiently.", "availability": {"title": "24/7 Availability", "description": "Our support team is available around the clock to assist you whenever you need help."}, "multilingual": {"title": "Multilingual Support", "description": "We provide support in multiple languages to serve our global community better."}, "dedicated": {"title": "Dedicated Team", "description": "Our experienced support specialists are dedicated to resolving your issues quickly."}}, "cta": {"title": "Need Quick Answers?", "description": "Check out our FAQ section for instant answers to common questions", "button": "View FAQ"}}, "howItWorks": {"title": "How Hala Works", "subtitle": "Your Journey to Luxury Living", "description": "Discover how easy it is to find and book your perfect accommodation with <PERSON><PERSON>'s seamless platform", "badge": "Simple & Seamless Process", "steps": {"search": {"title": "Search & Discover", "description": "Browse our curated collection of premium properties in the world's most desirable neighborhoods. Use our advanced filters to find exactly what you're looking for.", "features": {"0": "Advanced search filters", "1": "Real-time availability", "2": "Detailed property information", "3": "High-quality photos and virtual tours"}}, "book": {"title": "Book Instantly", "description": "Secure your reservation with our streamlined booking process. Complete your booking in minutes with our secure payment system.", "features": {"0": "Instant booking confirmation", "1": "Secure payment processing", "2": "Flexible cancellation policies", "3": "Digital contract management"}}, "experience": {"title": "Experience Luxury", "description": "Enjoy your stay with premium amenities and 24/7 support. Access everything you need through our WhatsApp concierge service.", "features": {"0": "WhatsApp concierge service", "1": "Premium amenities included", "2": "24/7 customer support", "3": "Local recommendations and experiences"}}}, "benefits": {"title": "Why Cho<PERSON>?", "subtitle": "Experience the Difference", "items": {"verified": {"title": "Verified Properties", "description": "Every property is personally inspected and verified to meet our luxury standards"}, "support": {"title": "24/7 Support", "description": "Round-the-clock assistance through WhatsApp and phone support"}, "technology": {"title": "Smart Technology", "description": "Seamless check-in, smart home features, and digital concierge services"}, "locations": {"title": "Prime Locations", "description": "Carefully selected properties in the most desirable neighborhoods worldwide"}}}, "app": {"title": "All-in-one Digital Experience", "subtitle": "Manage your stay and request services with just a few taps", "features": {"booking": {"title": "Book and manage your stays", "description": "Reserve properties, modify reservations, and view all booking details in one place."}, "checkIn": {"title": "Self check-in and check-out", "description": "Access keyless entry, follow step-by-step instructions, and check out smoothly."}, "trips": {"title": "View your upcoming trips", "description": "See your past and upcoming stays with dates, property information, and receipts."}, "support": {"title": "Customer support at your service", "description": "Get instant help via WhatsApp or chat, available 24/7."}}, "appStoreAlt": "Download on the App Store", "playStoreAlt": "Get it on Google Play"}, "cta": {"title": "Ready to Start Your Journey?", "description": "Join thousands of satisfied guests who have discovered luxury living with <PERSON><PERSON>", "button": "Explore Units"}}, "corporate": {"hero": {"title": "Hala Stays for Companies", "subtitle": "Flexible and modern solutions for employee accommodation", "cta": "Get Corporate Pricing"}, "valueProposition": {"title": "Make Business Travel Better", "description": "Your hala is ready for you. Spacious apartments equipped with technology give you the opportunity to manage your business affairs without any hassle while you're comfortable in your home."}, "accommodation": {"title": "Wide Range of Options", "description": "Our move-in ready properties are located in prime locations in major cities. Whether you're looking for an apartment, entire floor, or entire building, we have what you want.", "types": {"studio": {"title": "Studio or 1 Bedroom Apartment", "description": "Perfect for solo business travelers"}, "multi": {"title": "2, 3, or 4 Bedroom Apartment", "description": "Colleagues, groups, or families, there's room for everyone."}, "entire": {"title": "Entire Floor or Building", "description": "Flexible spaces to meet your employees' needs whether they're staying alone or with others."}}, "amenities": {"title": "What do we offer in every Stella?", "items": ["High-speed WiFi", "Air Conditioning", "Parking", "Washing Machine", "Collaborative Workspace", "Workstation", "Cleaning", "Fully Furnished", "Accessibility Features"]}, "features": {"oneBedroom": "1 Bedroom", "workDesk": "Work Desk", "kitchenette": "Kitchenette", "highSpeedWifi": "High-speed WiFi", "twoToFourBedrooms": "2-4 Bedrooms", "sharedWorkspace": "Shared Workspace", "fullKitchen": "Full Kitchen", "livingArea": "Living Area", "multipleUnits": "Multiple Units", "conferenceRoom": "Conference Room", "flexibleLayout": "Flexible Layout", "dedicatedSupport": "Dedicated Support"}}, "whyStella": {"title": "Why hala Stays?", "costSaving": {"title": "Cost Savings", "description": "Forget expensive cramped hotel rooms. Reduce your company's travel and accommodation costs compared to traditional hotels. Custom benefit for your company's needs with your dedicated account manager and rental flexibility."}, "productivity": {"title": "Increased Productivity", "description": "Everyone deserves a spacious apartment designed for work and life. Always equipped with a workstation, high-speed internet, full kitchen, washing machine, smart TVs and other amenities so the team can work and relax whenever they want."}, "flexibility": {"title": "Flexible Solutions", "description": "Our portfolio includes thousands of rental apartments in the Middle East, Europe, North America and North Africa. halais ready to welcome corporate guests for both long and short term."}}, "trustedBy": {"title": "We are trusted by business travelers and employees", "description": "Join thousands of companies worldwide who trust hala Stays for their corporate accommodation needs. From startups to Fortune 500 companies, we deliver exceptional service and quality accommodations."}, "amenities": {"wifi": "High-speed WiFi", "airConditioning": "Air Conditioning", "parking": "Parking", "washingMachine": "Washing Machine", "collaborativeWorkspace": "Collaborative Workspace", "workstation": "Workstation", "cleaning": "Cleaning", "fullyFurnished": "Fully Furnished", "accessibilityFeatures": "Accessibility Features"}, "cta": {"title": "Ready to Transform Your Corporate Travel?", "description": "Get in touch with our corporate team today and discover how hala Stays can help your company save costs, increase productivity, and provide better accommodation for your employees.", "button": "Contact Corporate Team"}}, "realEstate": {"hero": {"subtitle": "Premium Real Estate Partnership", "title": "Real Estate to Investors", "description": "Be part of the future of living", "cta": "Schedule a meeting"}, "partner": {"heading": "Partner with <PERSON><PERSON>", "description": "We manage and operate residential buildings in prime neighborhoods across selected cities worldwide.", "points": {"owners": "Building owners", "developers": "Real estate developers", "family": "Family office", "investors": "Individual investors", "reits": "Real estate funds"}}, "benefits": {"title": "Why Partner with <PERSON><PERSON>", "subtitle": "Discover the advantages of partnering with Hala to maximize your real estate investment.", "items": {"increase": {"title": "Increase Property Value", "description": "Benefit from our dynamic pricing, high occupancy rates, and expert marketing to boost your rental income."}, "protection": {"title": "Asset Protection", "description": "Our professional team ensures your property is maintained to the highest standards, preserving its value."}, "guaranteed": {"title": "Guaranteed Returns", "description": "Enjoy consistent rental income with our guaranteed return programs and long-term partnership agreements."}}}, "partnerTypes": {"title": "Who We Partner With", "description": "We work with various types of property owners and investors to create mutually beneficial partnerships.", "propertyOwners": {"title": "Property Owners", "description": "Individual property owners looking to maximize their rental income with professional management."}, "developers": {"title": "Real Estate Developers", "description": "Developers seeking reliable partners for long-term property management and guaranteed occupancy."}, "agents": {"title": "Real Estate Agents", "description": "Agents who want to offer their clients comprehensive property management solutions."}}, "steps": {"title": "How It Works", "description": "Simple steps to start your partnership with <PERSON><PERSON>", "submit": {"title": "Submit Property", "description": "Submit your building details and we will review them for partnership eligibility."}, "review": {"title": "Review & Assessment", "description": "Our team conducts a thorough review and provides design consultation to Hala standards."}, "onboarding": {"title": "Partnership Agreement", "description": "Sign a long-term partnership agreement and complete the onboarding process."}, "launch": {"title": "Launch & Earn", "description": "Start earning additional income without operational headaches while we handle everything."}}, "cta": {"title": "Ready to Partner with Us?", "description": "Join hundreds of property owners who trust Hala to manage their real estate investments.", "cta": "Schedule a Meeting"}, "contactModal": {"title": "Contact us", "description": "Let's discuss your property partnership opportunity", "name": "Name", "email": "Email", "mobile": "Mobile", "propertyLocation": "Where is your property located?", "message": "Tell us about yourself and your property. We will use this information for our selection process", "namePlaceholder": "Enter your full name", "emailPlaceholder": "Enter your email address", "mobilePlaceholder": "Enter your mobile number", "propertyLocationPlaceholder": "Enter property location (city, area, etc.)", "messagePlaceholder": "Please provide details about your property (type, size, current status) and tell us about yourself as a property owner...", "sendMessage": "Send Message", "sending": "Sending...", "successTitle": "Thank you for your inquiry!", "successMessage": "We'll get back to you within 24 hours to discuss your property.", "required": "*"}}, "careers": {"hero": {"badge": "Join Our Team", "title": "Build Your Career with Us", "subtitle": "Join a dynamic team that's revolutionizing the hospitality industry. Discover opportunities to grow, innovate, and make a meaningful impact.", "viewJobs": "View Open Positions", "learnMore": "Learn More"}, "values": {"badge": "Our Values", "title": "What We Stand For", "subtitle": "Our core values guide everything we do and shape the culture we've built together.", "items": {"passion": {"title": "Passion for Excellence", "description": "We're driven by a relentless pursuit of excellence in everything we do, from customer service to innovation."}, "teamwork": {"title": "Collaborative Spirit", "description": "We believe in the power of teamwork and foster an environment where diverse perspectives drive success."}, "innovation": {"title": "Innovation First", "description": "We embrace change and continuously seek innovative solutions to enhance the guest experience."}, "excellence": {"title": "Commitment to Excellence", "description": "We set high standards and are committed to delivering exceptional results in every aspect of our work."}}}, "benefits": {"badge": "Why Join Us", "title": "Benefits & Perks", "subtitle": "We offer comprehensive benefits and perks designed to support your personal and professional growth.", "items": {"salary": {"title": "Competitive Salary", "description": "We offer competitive compensation packages that reflect your skills, experience, and contributions."}, "insurance": {"title": "Health Insurance", "description": "Comprehensive health, dental, and vision insurance coverage for you and your family."}, "development": {"title": "Professional Development", "description": "Access to training programs, conferences, and educational opportunities to advance your career."}, "environment": {"title": "Great Work Environment", "description": "Modern offices, collaborative spaces, and a culture that promotes work-life balance."}, "transport": {"title": "Transportation Benefits", "description": "Transportation allowances and parking benefits to make your commute easier."}, "remote": {"title": "Flexible Work Options", "description": "Remote work opportunities and flexible schedules to help you maintain work-life balance."}}}, "jobs": {"badge": "Open Positions", "title": "Current Opportunities", "subtitle": "Explore our current job openings and find the perfect role to advance your career with us.", "viewDetails": "View Details", "apply": "Apply Now"}, "application": {"badge": "Apply Today", "title": "Ready to Join Us?", "subtitle": "Submit your application and take the first step towards an exciting career opportunity.", "form": {"name": "Full Name", "namePlaceholder": "Enter your full name", "email": "Email Address", "emailPlaceholder": "Enter your email address", "phone": "Phone Number", "phonePlaceholder": "Enter your phone number", "position": "Position Applied For", "positionPlaceholder": "Enter the position you're applying for", "experience": "Years of Experience", "experiencePlaceholder": "Enter your years of experience", "coverLetter": "Cover Letter", "coverLetterPlaceholder": "Tell us why you're interested in this position and what makes you a great fit...", "submit": "Submit Application"}, "success": "Application submitted successfully! We'll be in touch soon."}, "cta": {"title": "Don't See the Right Role?", "description": "We're always looking for talented individuals to join our team. Get in touch with us and let's explore how you can contribute to our mission.", "contact": "Contact Us", "newsletter": "Join <PERSON>letter"}}, "auth": {"loginFirstDialog": {"title": "Login First", "description": "Please login to your account to do this action", "loginNow": "Login Now"}, "welcome": "Welcome to Hala", "signInSignUp": "Sign in to your account or create a new one", "signIn": "Sign In", "signUp": "Sign Up", "email": "Email", "password": "Password", "confirmPassword": "Confirm Password", "firstName": "First Name", "lastName": "Last Name", "phoneNumber": "Phone Number", "dateOfBirth": "Date of Birth", "nationality": "Nationality", "identityDocuments": "Upload Identity Documents", "fullName": "Full Name", "enterEmail": "Enter your email", "enterPassword": "Enter your password", "createPassword": "Create a password", "confirmYourPassword": "Confirm your password", "enterFullName": "Enter your full name", "enterFirstName": "Enter your first name", "enterLastName": "Enter your last name", "enterPhoneNumber": "Enter your phone number", "selectDateOfBirth": "Select your date of birth", "selectNationality": "Select your nationality", "uploadDocuments": "Upload your identity documents", "rememberMe": "Remember me", "forgotPassword": "Forgot password?", "signingIn": "Signing in...", "creatingAccount": "Creating account...", "createAccount": "Create Account", "backToHome": "Back to Home", "demoCredentials": "Demo credentials: <EMAIL> / password", "agreeToTerms": "I agree to the", "termsOfService": "Terms of Service", "and": "and", "privacyPolicy": "Privacy Policy", "byContinuing": "By continuing, you agree to <PERSON><PERSON>'s", "profile": "Profile", "settings": "Settings", "signOut": "Sign Out", "loginSuccessful": "Login successful! Redirecting...", "registrationSuccessful": "Registration successful! Redirecting...", "fillAllFields": "Please fill in all fields", "passwordsDontMatch": "Passwords do not match", "passwordTooShort": "Password must be at least 6 characters long", "invalidCredentials": "Invalid email or password", "loginFailed": "<PERSON><PERSON> failed. Please try again.", "registrationFailed": "Registration failed. Please try again.", "fileUploadError": "Please select a file to upload", "fileSizeError": "File size must be less than 5MB", "fileTypeError": "Please upload PDF, JPG, or PNG files only", "resetPassword": "Reset Password", "resetPasswordDescription": "Enter a new password for your account.", "resettingPassword": "Resetting Password...", "passwordResetSuccess": "Password Reset Successful", "passwordResetSuccessMessage": "Your password has been reset successfully. You can now log in with your new password.", "emailConfirmation": "Email Verification", "emailConfirmationTitle": "Verify Your Email Address", "emailConfirmationDescription": "We're verifying your email address to complete your account setup.", "confirmingEmail": "Confirming Your Email", "confirmingEmailDescription": "Please wait while we verify your email address...", "emailConfirmedSuccess": "Email Confirmed Successfully!", "emailConfirmedSuccessMessage": "Welcome to Hala! Your email has been verified. Redirecting to home page in {countdown} seconds...", "emailConfirmationFailed": "Confirmation Failed", "emailConfirmationFailedMessage": "We couldn't verify your email. The link may be expired or invalid.", "invalidConfirmationLink": "Invalid Confirmation Link", "invalidConfirmationLinkMessage": "The confirmation link is missing required information.", "tryAgain": "Try Again", "goToHome": "Go to Home", "continueToLogin": "Continue to Login", "loading": "Loading...", "signup": {"success": {"title": "Account created successfully!", "description": "Please check your email for confirmation"}}, "forgetPasswordFeedback": {"title": "Check Your Email", "description": "We've sent you an email with instructions to reset your password. Please check your inbox and spam folder."}, "forgotPasswordTitle": "Forgot Password?", "forgotPasswordDescription": "Enter your email address and we'll send you a link to reset your password.", "forgotPasswordEmailNotFound": "No account found with this email address.", "sendResetEmail": "Send Reset Email", "sendingResetEmail": "Sending...", "resetEmailSentTitle": "Check Your Email", "resetEmailSentDescription": "We've sent you an email with instructions to reset your password. Please check your inbox and spam folder.", "rememberPassword": "Remember your password?", "validations": {"required": "This field is required", "email": "Please enter a valid email address"}, "errors": {"generalError": "Something went wrong"}, "global": {"cancel": "Cancel"}}}