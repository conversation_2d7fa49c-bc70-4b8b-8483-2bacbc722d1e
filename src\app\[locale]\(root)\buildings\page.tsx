"use client";
import { useGetBuildings, useGetBuildingsByCity } from "@/hooks/buildings";
import { useLocale, useTranslations } from "next-intl";
import BuildingHeader from "./components/BuildingHeader";
import BuildingCards from "./components/BuildingCards";
import { Card } from "@/components/ui/card";

export default function page() {
  const locale = useLocale();
  const t = useTranslations("cities");

  const { data } = useGetBuildingsByCity({ pageSize: 50 });
  const { data: AllBuilding } = useGetBuildings({
    pageSize: 4,
  });

  return (
    <div className="flex-1 p-12">
      <div className="mx-auto max-w-7xl">
        <BuildingHeader />
        <BuildingCards data={data?.items ?? []} />
        <div className="text-center">see more buildings</div>
        <BuildingCards data={AllBuilding?.items ?? []} />
      </div>
    </div>
  );
}
