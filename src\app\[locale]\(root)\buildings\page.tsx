"use client";
import { useGetBuildings, useGetBuildingsByCity } from "@/hooks/buildings";
import { useLocale, useTranslations } from "next-intl";
import BuildingHeader from "./components/BuildingHeader";
import BuildingCards from "./components/BuildingCards";
import { Building2, MapPin, Search, Filter } from "lucide-react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useState, useMemo } from "react";

export default function BuildingsPage() {
  const locale = useLocale();
  const t = useTranslations("buildings");
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCity, setSelectedCity] = useState<string>("");

  const { data: buildingsByCity, isLoading: isLoadingByCity } =
    useGetBuildingsByCity({ pageSize: 50 });
  const { data: allBuildings, isLoading: isLoadingAll } = useGetBuildings({
    pageSize: 20,
  });

  // Combine and filter buildings
  const allBuildingsData = useMemo(() => {
    const cityBuildings = buildingsByCity?.items ?? [];
    const otherBuildings = allBuildings?.items ?? [];

    // Combine and remove duplicates
    const combined = [...cityBuildings];
    otherBuildings.forEach((building) => {
      if (!combined.find((b) => b.id === building.id)) {
        combined.push(building);
      }
    });

    return combined;
  }, [buildingsByCity?.items, allBuildings?.items]);

  // Filter buildings based on search and city
  const filteredBuildings = useMemo(() => {
    return allBuildingsData.filter((building) => {
      const matchesSearch =
        searchQuery === "" ||
        (locale === "ar" ? building.nameAr : building.nameEn)
          .toLowerCase()
          .includes(searchQuery.toLowerCase()) ||
        (locale === "ar" ? building.cityNameAr : building.cityNameEn)
          .toLowerCase()
          .includes(searchQuery.toLowerCase());

      const matchesCity =
        selectedCity === "" ||
        (locale === "ar" ? building.cityNameAr : building.cityNameEn) ===
          selectedCity;

      return matchesSearch && matchesCity;
    });
  }, [allBuildingsData, searchQuery, selectedCity, locale]);

  // Get unique cities for filter
  const uniqueCities = useMemo(() => {
    const cities = allBuildingsData.map((building) =>
      locale === "ar" ? building.cityNameAr : building.cityNameEn,
    );
    return Array.from(new Set(cities)).sort();
  }, [allBuildingsData, locale]);

  const isLoading = isLoadingByCity || isLoadingAll;
  const featuredBuildings = buildingsByCity?.items?.slice(0, 4) ?? [];

  return (
    <div className="min-h-screen bg-gray-50/50">
      <div className="mx-auto max-w-7xl px-4 py-8 sm:px-6 lg:px-8">
        <BuildingHeader />

        {/* Search and Filter Section */}
        <Card className="shadow-soft mb-8 border-0 bg-white">
          <div className="p-6">
            <div className="flex flex-col gap-4 md:flex-row md:items-center md:justify-between">
              <div className="max-w-md flex-1">
                <div className="relative">
                  <Search className="absolute top-1/2 left-3 h-4 w-4 -translate-y-1/2 text-gray-400" />
                  <Input
                    placeholder={
                      t("searchPlaceholder") || "Search buildings..."
                    }
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>

              <div className="flex items-center gap-3">
                <Filter className="h-4 w-4 text-gray-500" />
                <div className="flex flex-wrap gap-2">
                  <Badge
                    variant={selectedCity === "" ? "default" : "outline"}
                    className="cursor-pointer"
                    onClick={() => setSelectedCity("")}
                  >
                    {t("allCities") || "All Cities"}
                  </Badge>
                  {uniqueCities.map((city) => (
                    <Badge
                      key={city}
                      variant={selectedCity === city ? "default" : "outline"}
                      className="cursor-pointer"
                      onClick={() => setSelectedCity(city)}
                    >
                      {city}
                    </Badge>
                  ))}
                </div>
              </div>
            </div>

            {/* Results Summary */}
            <div className="mt-4 flex items-center justify-between border-t pt-4">
              <div className="flex items-center gap-2 text-sm text-gray-600">
                <Building2 className="h-4 w-4" />
                <span>
                  {filteredBuildings.length}{" "}
                  {t("buildingsFound") || "buildings found"}
                  {selectedCity && ` in ${selectedCity}`}
                </span>
              </div>
              {(searchQuery || selectedCity) && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setSearchQuery("");
                    setSelectedCity("");
                  }}
                >
                  {t("clearFilters") || "Clear filters"}
                </Button>
              )}
            </div>
          </div>
        </Card>

        {/* Featured Buildings Section */}
        {featuredBuildings.length > 0 && !searchQuery && !selectedCity && (
          <section className="mb-12">
            <div className="mb-6 flex items-center gap-3">
              <div className="flex items-center gap-2">
                <MapPin className="text-primary h-5 w-5" />
                <h2 className="text-2xl font-bold text-gray-900">
                  {t("featuredBuildings") || "Featured Buildings"}
                </h2>
              </div>
              <Badge className="bg-primary/10 text-primary">
                {t("popular") || "Popular"}
              </Badge>
            </div>
            <BuildingCards
              data={featuredBuildings}
              isLoading={isLoadingByCity}
            />
          </section>
        )}

        {/* All Buildings Section */}
        <section>
          <div className="mb-6">
            <h2 className="text-2xl font-bold text-gray-900">
              {searchQuery || selectedCity
                ? t("searchResults") || "Search Results"
                : t("allBuildings") || "All Buildings"}
            </h2>
            <p className="text-gray-600">
              {t("browseDescription") ||
                "Browse our complete collection of premium buildings"}
            </p>
          </div>
          <BuildingCards data={filteredBuildings} isLoading={isLoading} />
        </section>
      </div>
    </div>
  );
}
